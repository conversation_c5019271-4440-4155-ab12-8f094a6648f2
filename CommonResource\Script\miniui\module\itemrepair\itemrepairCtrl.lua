--声明
local itemrepairCtrl = Class("itemrepairCtrl",ClassList["UIBaseCtrl"])

--创建
function itemrepairCtrl:Create(param)
	return ClassList["itemrepairCtrl"].new(param)
end

--初始化
function itemrepairCtrl:Init(param)
	self.super:Init(param)

end

function itemrepairCtrl:btnrepairClick(obj, context)
	if not ClientBackpack or not CurMainPlayer then
		return
	end

	local itemId = ClientBackpack:getGridItem(STORAGE_START_INDEX)
	if itemId == 0 then
		return
	end

	local tooldef = ToolDefCsv:get(itemId)
	if not tooldef then
		return
	end

	if not self.mat_enough then
		ShowGameTips(GetS(700001))
		return
	end

	if CurMainPlayer then
		CurMainPlayer:repair(STORAGE_START_INDEX)
	end
end

--启动
function itemrepairCtrl:Start()
	-- MiniLog("itemrepairCtrl:Start")
	self.super:Start()
	self.view:InitView()
	self.mat_enough = false

	self.target_toolitem = self.view.widgets.targetitem
	self.toolitem_ctrl = self.target_toolitem:getController("ctrl")
	self:UpdateTargetToolItem()

	local eventdispatcher = GetInst("MiniUIEventDispatcher")
	local dragctrl = GetInst("MiniUIManager"):GetDragCtrl()
	if dragctrl then
		eventdispatcher:addEventListener(self.target_toolitem, UIEventType_TouchBegin, function(obj, context)
			dragctrl:DragStart(STORAGE_START_INDEX, obj, context, function(grid_index)
				return self:CheckCanRepair(grid_index)
			end)
			self:UpdateTargetToolItem()
		end)

		eventdispatcher:addEventListener(self.target_toolitem, UIEventType_RightClick, function(obj, context)
			self:TryGetRepairItem()
		end)

		dragctrl:RegisterDragEndListener("itemrepairCtrl:OnDragEnd", function(endpos, start_index, start_count)
			return self:OnDragEnd(endpos, start_index, start_count)
		end, function()
			self:UpdateTargetToolItem()
		end)
	end

	self.backpack_change_listener = SubscribeGameEvent(nil, "GE_BACKPACK_CHANGE", function (context)
        -- MiniLog("itemrepairCtrl:GE_BACKPACK_CHANGE")
		if self.isprocessing_change then
			return
		end
		self.isprocessing_change = true
		threadpool:work(function()
			threadpool:wait(0.1)
			self:UpdateTargetToolItem()
			self.isprocessing_change = false
		end)
    end)
	-- self.playerdowned_listener = SubscribeGameEvent(nil, "GE_PLAYER_DOWNED", function (context)
    --     -- MiniLog("itemrepairCtrl:GE_PLAYER_DOWNED")
	-- 	GetInst("MiniUIManager"):CloseUI("itemrepairAutoGen")
    -- end)
end

--刷新
function itemrepairCtrl:Refresh()
	-- MiniLog("itemrepairCtrl:Refresh")
	self.super:Refresh()

	self:UpdateTargetToolItem()
end

--关闭
function itemrepairCtrl:Remove()
	-- MiniLog("itemrepairCtrl:Remove")
	self.super:Reset()

	local dragctrl = GetInst("MiniUIManager"):GetDragCtrl()
	if dragctrl then
		dragctrl:UnRegisterDragEndListener("itemrepairCtrl:OnDragEnd")
	end

	if CurMainPlayer then
		CurMainPlayer:closeContainer()
	end

	UnsubscribeGameEvent(nil, "GE_BACKPACK_CHANGE", self.backpack_change_listener)
	UnsubscribeGameEvent(nil, "GE_PLAYER_DOWNED", self.playerdowned_listener)
end

--隐藏
function itemrepairCtrl:Reset()
	-- MiniLog("itemrepairCtrl:Reset")
	self.super:Reset()

	if CurMainPlayer then
		CurMainPlayer:closeContainer()
	end
end

-- -1表示放回原位
function itemrepairCtrl:OnDragEnd(endpos, start_index, start_count)
	-- MiniLog("itemrepairCtrl:OnDragEnd", endpos, start_index, start_count)
	if not UIUtils:PosInObj(endpos, self.view.widgets.background) then
		return
	end

	-- 判断是否是工具
	local handgridindex = MOUSE_PICKITEM_INDEX + 1

	if not self:CheckCanRepair(handgridindex) then
		ShowGameTips(GetS(700004))
		return -1
	end

	if UIUtils:PosInObj(endpos, self.target_toolitem) then
		return STORAGE_START_INDEX
	end
end

function itemrepairCtrl:ResetPanel()
	self.toolitem_ctrl:setSelectedIndex(0)
	self:SetUIState(0)
	self:ClearCostItem()
end

function itemrepairCtrl:SetUIState(state)
	self.view.root_ctrl:setSelectedIndex(state)
	if state == 0 then
		self.view.widgets.errtip:setText(GetS(700005))
	elseif state == 3 then
		self.view.widgets.errtip:setText(GetS(700002))
	end
end

function itemrepairCtrl:UpdateTargetToolItem()
	MiniLog("itemrepairCtrl:UpdateTargetToolItem")
	if not ClientBackpack or not CurMainPlayer then
		return
	end

	local gridindex = STORAGE_START_INDEX 
    local itemId = ClientBackpack:getGridItem(gridindex)
	if itemId == 0 then
		self:ResetPanel()
		return
	end
	self.target_itemid = itemId
	self.toolitem_ctrl:setSelectedIndex(1)
	
	local maxDuration = ClientBackpack:getGridMaxDuration(gridindex)
	local duration = ClientBackpack:getGridDuration(gridindex)

	UIUtils:SetItemIcon(self.view.widgets.targetitem_icon, itemId)
	UIUtils:SetItemDuration(self.view.widgets.targetitem_bar, duration, maxDuration)

    -- 更新维修材料
    local enough = self:UpdateCostItem()
	self.mat_enough = enough

	if not enough then
		self:SetUIState(1)
	elseif duration == maxDuration then
		self:SetUIState(3)
	else
		self:SetUIState(2)
	end
end

function itemrepairCtrl:UpdateCostItem()
	if not self.target_itemid then
		self:ClearCostItem()
		return false
	end

    local tooldef = ToolDefCsv:get(self.target_itemid)
    if not tooldef then
		self:ClearCostItem()
        return false
    end

	local enough = true
	local maxDuration = ClientBackpack:getGridMaxDuration(STORAGE_START_INDEX)
	local duration = ClientBackpack:getGridDuration(STORAGE_START_INDEX)

	local costitems = GetToolRepairCost(self.target_itemid, duration, maxDuration)
    for i, costui in ipairs(self.view.costitems) do
		if costitems[i] then
			local itemid = costitems[i].id
			local num = costitems[i].num
			costui.ctrl:setSelectedIndex(1)
			UIUtils:SetItemIcon(costui.icon, itemid)
			local havenum = ClientBackpack:getItemCountInNormalPack(itemid)
			if havenum >= num then
				costui.num:setText(havenum .. "/" .. num)
			else
				costui.num:setText("<font color='#f07d7d'>" .. havenum .. "/" .. num .. "</font>")
			end

			local itemdef = DefMgr:getItemDef(itemid)
			if itemdef then
				costui.txt:setText(itemdef.Name)
			else
				costui.txt:setText("")
			end

			if havenum < num then
				enough = false
			end
		else
			costui.ctrl:setSelectedIndex(0)
			costui.txt:setText("")
		end
	end
    return enough
end

function itemrepairCtrl:ClearCostItem()
	for _, costitem in ipairs(self.view.costitems) do
		costitem.ctrl:setSelectedIndex(0)
	end
end


function itemrepairCtrl:CheckCanRepair(grid_index)
	if not ClientBackpack or not CurMainPlayer then
		return false
	end
	
	local itemId = ClientBackpack:getGridItem(grid_index)
	if itemId == 0 then
		return false
	end

	local tooldef = ToolDefCsv:get(itemId)
	if not tooldef then
		return false
	end
	-- 工具没有耐久度
	if tooldef.Duration == 0 then
		return false
	end
	-- 没有配置修复材料
	local hasrepair = false
	for i = 0, 5 do
		if tooldef.RepairId[i] ~= 0 then
			hasrepair = true
			break
		end
	end
	if not hasrepair then
		return false
	end
	return true
end


function itemrepairCtrl:TryPutRepairItem(start_index)
	if not ClientBackpack or not CurMainPlayer then
		return
	end

	if not self:CheckCanRepair(start_index) then
		ShowGameTips(GetS(700004))
		return
	end

	CurMainPlayer:moveItem(start_index, STORAGE_START_INDEX, 1)
	self:UpdateTargetToolItem()
end

function itemrepairCtrl:TryGetRepairItem()
	if not ClientBackpack or not CurMainPlayer then
		return
	end

	local itemId = ClientBackpack:getGridItem(STORAGE_START_INDEX)
	if itemId == 0 then
		return
	end
	
	CurMainPlayer:lootItem(STORAGE_START_INDEX, 1)

	self:UpdateTargetToolItem()
end


--消息处理
function itemrepairCtrl:FGUIHandleEvent(eventName)

end

