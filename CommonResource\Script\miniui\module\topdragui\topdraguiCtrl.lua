--声明
local topdraguiCtrl = Class("topdraguiCtrl",ClassList["UIBaseCtrl"])

--创建
function topdraguiCtrl:Create(param)
	return ClassList["topdraguiCtrl"].new(param)
end

--初始化
function topdraguiCtrl:Init(param)
	self.super:Init(param)

end

--启动
function topdraguiCtrl:Start()
	self.super:Start()
	self.view:InitView()
	self.moveitem = self.view.widgets.moveitem
	self.moveiteminfo = self.view.moveiteminfo
	self.drag_end_listeners = {}

	GetInst("MiniUIEventDispatcher"):addEventListener(self.moveitem, UIEventType_TouchEnd, function(obj, context)
		self:DragEnd(obj, context)
	end)
end

--刷新
function topdraguiCtrl:Refresh()
	self.super:Refresh()

end

--隐藏
function topdraguiCtrl:Reset()
	self.super:Reset()

end

--关闭
function topdraguiCtrl:Remove()
	self.super:Reset()

	self.swap_check_func = nil
	self.start_index = nil
	self.start_count = nil
end

--消息处理
function topdraguiCtrl:FGUIHandleEvent(eventName)

end

-- swap_check_func 检查交换 如果可以交换返回true 否则返回false
function topdraguiCtrl:DragStart(start_index, obj, context, swap_check_func)
	local itemId = ClientBackpack:getGridItem(start_index)
	local count = ClientBackpack:getGridNum(start_index)
	MiniLog("topdraguiCtrl:DragStart", start_index, itemId, count)

	if itemId == 0 or count == 0 then
		MiniLog("topdraguiCtrl:DragStart itemId == 0 or count == 0", start_index, itemId, count)
		-- self.moveitem:setVisible(false)
		return
	end

	self.swap_check_func = swap_check_func
	self.start_index = start_index
	self.start_count = count

	-- self.moveitem:setVisible(true)
	self:UpdateMoveItemPos(context)
	
	UIUtils:SetItemIcon(self.moveiteminfo.icon, itemId)

	CurMainPlayer:moveItem(start_index, MOUSE_PICKITEM_INDEX + 1, self.start_count)

	GetInst("MiniUIManager"):ShowUI("topdraguiAutoGen")
	self.drag_move_listener = GetInst("MiniUIEventDispatcher"):addEventListener(self.view.root, UIEventType_MouseMove, function(obj, context)
		self:DragMove(obj, context)
	end)
end

function topdraguiCtrl:DragMove(obj, context)
	if not self.drag_move_listener then
		return
	end

	self:UpdateMoveItemPos(context)
end

function topdraguiCtrl:UpdateMoveItemPos(context)
	local mouse_pos = context:getInput():getPosition()
	self.moveitem:setPosition(mouse_pos.x, mouse_pos.y)
end

-- 注册拖拽结束回调 callback返回目标gridindex
function topdraguiCtrl:RegisterDragEndListener(name, find_callback, after_callback)
	if self.drag_end_listeners[name] then
		MiniLog("topdraguiCtrl:RegisterDragEndListener already registered, error!", name)
		return
	end

	self.drag_end_listeners[name] = {find_callback = find_callback, after_callback = after_callback}
end

function topdraguiCtrl:UnRegisterDragEndListener(name)
	self.drag_end_listeners[name] = nil
end


function topdraguiCtrl:DragEnd(obj, context)
	MiniLog("topdraguiCtrl:DragEnd", obj, context)
	-- self.moveitem:setVisible(false)
	GetInst("MiniUIManager"):HideUI("topdraguiAutoGen")

	if self.drag_move_listener then
		GetInst("MiniUIEventDispatcher"):removeEventListener(self.view.root, self.drag_move_listener)
		self.drag_move_listener = nil
	end

	-- grid_index 目标gridindex
	-- nil表示需要丢弃  -1 表示放回原位
	local endpos = context:getInput():getPosition()
	local grid_index = nil
	local after_callback = nil
	for _, callbacks in pairs(self.drag_end_listeners) do
		grid_index = callbacks.find_callback(endpos, self.start_index, self.start_count)
		if grid_index then
			after_callback = callbacks.after_callback
			break
		end
	end
	MiniLog("topdraguiCtrl:DragEnd grid_index", self.start_index, self.start_count, grid_index)

	local mouseindex = MOUSE_PICKITEM_INDEX + 1
	if grid_index then
		-- 放回原位
		if grid_index == self.start_index or grid_index == -1 then
			CurMainPlayer:moveItem(mouseindex, self.start_index, self.start_count)
		else
			local target_itemId = ClientBackpack:getGridItem(grid_index)
			local target_count = ClientBackpack:getGridNum(grid_index)

			-- 检查是否可以交换
			if target_itemId ~= 0 then
				if not self.swap_check_func or self.swap_check_func(grid_index) then
					CurMainPlayer:moveItem(grid_index, self.start_index, target_count)
					CurMainPlayer:moveItem(mouseindex, grid_index, self.start_count)
				else
					-- 不能交换，放回原位
					CurMainPlayer:moveItem(mouseindex, self.start_index, self.start_count)
				end
			else
				CurMainPlayer:moveItem(mouseindex, grid_index, self.start_count)
			end
		end

		if after_callback then
			after_callback(grid_index)
		end
	else
		-- 丢弃
		CurMainPlayer:discardItem(mouseindex, self.start_count)
	end

	-- 刷新背包  TODO 应该改成事件通知
	local playermainCtrl = GetInst("MiniUIManager"):GetCtrl("playermain")
	if playermainCtrl then
		playermainCtrl:UpdateBackpack(true)
	end
end
