#include "ActorCubeChest.h"
#include "ClientPlayer.h"
#include "PlayerControl.h"
#include "PlayerStateController.h"
#include "world.h"
#include "ClientActorManager.h"
#include "LivingLocoMotion.h"
#include "BlockMaterialMgr.h"
#include "WorldManager.h"
#include "WorldRender.h"
#include "PlayerLocoMotion.h"
#include "EffectManager.h"
#include "special_blockid.h"
#include "DefManagerProxy.h"
#include "MpActorManager.h"
#include "worlddisplay/BlockScene.h"

#include "GameNetManager.h"
#include "Entity/OgreEntity.h"
#include "Entity/OgreModel.h"
#include "OgrePhysXManager.h"
#include "ObserverEventManager.h"
#include "ChestLocomotion.h"
#include "BindActorComponent.h"
#include "OgreUtils.h"
#include "SoundComponent.h"
#include "BindActorComponent.h"
#include "BindActorComponent.h"
#include "ParticlesComponent.h"
#include "ActorBodySequence.h"
#include "ActorVehicleAssemble.h"
#include "VehicleWorld.h"
#include "UGCEntity.h"
#include "UGCModelLoader.h"
#include "SandboxGameDef.h"
#include "UgcAssetHeader.h"
using namespace MINIW;
IMPLEMENT_SCENEOBJECTCLASS(ActorCubeChest)
using namespace Rainbow;
using namespace MNSandbox;


#define LocoMotion ChestLocoMotion

ActorCubeChest::ActorCubeChest() : m_Entity(NULL)
{
	CreateComponent<LocoMotion>("ChestLocoMotion");
	//CreateComponent <PhysicsLocoMotion>("PhysicsLocoMotion");
	m_SpawnPos = WCoord(0, 0, 0);
	m_InvalidCatchTick = 0;
	model_id = 0;

	m_Dribbing = false;
	m_KeepdribbingTick = 0;
	m_DribbingAngle = 0;

	m_KnockUpInvalidActor = NULL;
	createEvent();

	def_Width = 2;
	def_Height = 3;
	def_Deepth = 6;
}

ActorCubeChest::~ActorCubeChest()
{
	Entity::Destory(m_Entity); m_Entity = nullptr;

	m_KnockUpInvalidActor = NULL;
	LOG_INFO("==ActorCubeChest::~ActorCubeChest()");

}

void ActorCubeChest::init()
{
#ifndef IWORLD_SERVER_BUILD
	m_Entity = Entity::Create();
	//  11000049  礼盒
	UGCEntity* ugcEntity = UGCEntity::Create();
	m_Entity = ugcEntity;
	ugcEntity->LoadModelAsync("ugcModel/11000049/body.obj", UgcAssetType::OBJ, [this](bool success, UGCModelLoader* modelLoader)->void
		{
			if (success && m_Entity && modelLoader)
			{
				Rainbow::Model* model = modelLoader->GetModel();
				if (model)
				{
					m_Entity->Load(model);
					//m_ModelIsLoading = false;
				}
			}
		});


	m_Entity->PlayAnim(100100);
	m_Entity->SetInstanceAmbient(ColourValue(0.2f, 0.2f, 0.2f));
	if (m_pWorld != nullptr && m_pWorld->getScene())
	{
		m_pWorld->getScene()->AddGameObject(m_Entity->GetGameObject());
	}
	float modelScale = 300.0f; // 使用与def->ModelScale相同的缩放比例
	m_Entity->SetScale(Rainbow::Vector3f(modelScale, modelScale, modelScale));

#endif


	// 设置模型缩放比例
	// 游戏中BLOCK_SIZE=100表示1个方块是100厘米（1米）
	// WCoord类使用厘米作为单位
	 

	// 根据模型实际尺寸设置碰撞盒参数（单位：厘米）
	// X轴方向尺寸: 约 0.4093厘米
	// Y轴方向尺寸: 约 0.5213厘米
	// Z轴方向尺寸: 约 1.0007厘米
	float Height = this->def_Height;// 高度对应Y轴方向（约0.52f厘米）
	float Width = this->def_Width; // 宽度对应X轴方向（约0.41厘米）
	float depth = this->def_Deepth;


	if (getLocoMotion())
	{
		getLocoMotion()->setBoundCuboid((int)(Height * 100), (int)(Width * 100), (int)(depth * 100));
		getLocoMotion()->setAttackBound((int)(Height * 100), (int)(Width * 100), (int)(depth * 100));
		getLocoMotion()->m_yOffset = getLocoMotion()->m_BoundHeight / 2;
	}

	m_SpawnPos = getPosition();
	m_ServerYawCmp = getLocoMotion()->m_RotateQuat.ToUInt32();
	setItemId(ms_itemID);
}

int ActorCubeChest::getMass()
{
	return 600000;
}


void ActorCubeChest::createEvent()
{
	typedef ListenerFunctionRef<const char*, bool, float> ListenerPlayBodyEffect;
	ListenerPlayBodyEffect* listenerPlayBodyEffect = SANDBOX_NEW(ListenerPlayBodyEffect, [&](const char* fxname, bool sync, float loopPlayTime) -> void {
		this->playBodyEffect(fxname, sync, loopPlayTime);
		});
	Event2().Subscribe("playBodyEffect_key_1", listenerPlayBodyEffect);

	typedef ListenerFunctionRef<const char*, bool> ListenerStopBodyEffect;
	ListenerStopBodyEffect* listenerStopBodyEffect = SANDBOX_NEW(ListenerStopBodyEffect, [&](const char* fxname, bool sync) -> void {
		this->stopBodyEffect(fxname, sync);
		});
	Event2().Subscribe("stopBodyEffect_key_1", listenerStopBodyEffect);

	typedef ListenerFunctionRef<const char*, float, float> ListenerPlayBodyEffectClient;
	ListenerPlayBodyEffectClient* listenerPlayBodyEffectClient = SANDBOX_NEW(ListenerPlayBodyEffectClient, [&](const char* fxname, float scale, float loopPlayTime) -> void {
		this->playBodyEffectClient(fxname, scale, loopPlayTime);
	});
	Event2().Subscribe("playBodyEffect_key_client", listenerPlayBodyEffectClient);

	typedef ListenerFunctionRef<const char*, bool> ListenerStopBodyEffectClient;
	ListenerStopBodyEffectClient* listenerStopBodyEffectClient = SANDBOX_NEW(ListenerStopBodyEffectClient, [&](const char* fxname, bool sync) -> void {
		this->stopBodyEffect(fxname, sync);
	});
	Event2().Subscribe("stopBodyEffect_key_client", listenerStopBodyEffectClient);
}


int ActorCubeChest::getObjType() const
{
	return OBJ_TYPE_CUBECHEST;
}

void ActorCubeChest::tick()
{
	ClientActor::tick();
	if(getPosition().y < -64*BLOCK_SIZE)
	{
		kill();
	}

	if(!m_pWorld->isRemoteMode())
	{
		CollideAABB box;
		getCollideBox(box);
		box.expand(20, 0, 20);

		std::vector<IClientActor *>actors;
		m_pWorld->getActorsInBoxExclude(actors, box, this);

		for(size_t i=0; i<actors.size(); i++)
		{
			ClientActor* pactor = actors[i]->GetActor();
			if(pactor->canBePushed())
			{
				auto bindAComponent = getBindActorCom();
				if (bindAComponent && bindAComponent->getTarget() != pactor)
				{
					pactor->applyActorCollision(this);
				}
			}
		}
	}

#ifndef IWORLD_SERVER_BUILD
	Vector4f lightparam(0,0,0,0);
	m_pWorld->getBlockLightValue2_Air(lightparam.x, lightparam.y, CoordDivBlock(getPosition()));
	m_Entity->SetInstanceData(lightparam);
#endif

	if (m_InvalidCatchTick > 0)
	{
		m_InvalidCatchTick--;
		if (m_InvalidCatchTick <= 0 && m_KnockUpInvalidActor)
			m_KnockUpInvalidActor = NULL;
	}

	if (m_KeepdribbingTick > 0)
	{
		m_KeepdribbingTick--;
		if (m_KeepdribbingTick == 0)
			m_Dribbing = false;
	}

	ClientPlayer *player = nullptr;
	auto bindAComponent = getBindActorCom();
	if (bindAComponent)
	{
		player = dynamic_cast<ClientPlayer *>(bindAComponent->getTarget());
	}
	if (player && m_Dribbing)
	{
		m_DribbingAngle += m_DribbingMotion.Length() * 0.7f;
		m_DribbingAngle = (float)((int)m_DribbingAngle % 360);
		if(m_Entity)
		{
			m_Entity->SetRotation(player->getLocoMotion()->m_RotateYaw, -m_DribbingAngle, 0);
			m_Entity->UpdateTick(1);
		}
	}
}

void ActorCubeChest::update(float dtime)
{
	ClientActor::update(dtime);

	{
		ChestLocoMotion *loc = static_cast<ChestLocoMotion *>(getLocoMotion());
		if (!m_Entity)
			return;
		m_Entity->SetPosition(loc->m_UpdatePos);
		//m_Entity->setRotation(loc->m_UpdateRot);

		ClientPlayer *player = nullptr;
		auto bindAComponent = getBindActorCom();
		if (bindAComponent)
		{
			player = dynamic_cast<ClientPlayer *>(bindAComponent->getTarget());
		}
		if (player && m_Dribbing)
		{
			/*float addangle = m_DribbingMotion.length()*0.15;
			LOG_INFO("kekeke addangle:%f  dtime:%f", addangle, dtime);
			m_DribbingAngle += m_DribbingMotion.length()*15 * dtime;

			m_DribbingAngle = (int)m_DribbingAngle % 360;

			m_Entity->SetRotation(player->getLocoMotion()->m_RotateYaw, -m_DribbingAngle, 0);*/
		}
		else if (!player)
		{
			m_Entity->SetRotation(loc->m_UpdateRot);
		}
		// TODO 控件内部自己有Tick
		//m_Entity->Update(TimeToTick(dtime));
	}
}

void ActorCubeChest::enterWorld(World *pworld)
{
	ClientActor::enterWorld(pworld);
	if (m_Entity && pworld->getScene())
	{
		pworld->getScene()->AddGameObject(m_Entity->GetGameObject());
	}
#ifdef USE_PHYSX
	static_cast<ChestLocoMotion *>(getLocoMotion())->checkPhysWorld();
	static_cast<ChestLocoMotion *>(getLocoMotion())->attachPhysActor();
#endif
}

void ActorCubeChest::leaveWorld(bool keep_inchunk)
{
#ifdef USE_PHYSX
	static_cast<ChestLocoMotion *>(getLocoMotion())->detachPhysActor();
	static_cast<ChestLocoMotion *>(getLocoMotion());
#endif

	ClientActor::leaveWorld(keep_inchunk);
}

flatbuffers::Offset<FBSave::SectionActor> ActorCubeChest::save(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	auto actor = FBSave::CreateActorCubeChest(builder, basedata, model_id);

	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorCubeChest, actor.Union());

}

bool ActorCubeChest::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorCubeChest *>(srcdata);
	loadActorCommon(src->basedata());

	model_id = src->itemid();
	init();
	return true;
}

void ActorCubeChest::onCollideWithPlayer(ClientActor *player)
{

}

bool ActorCubeChest::tryCatchBall(ClientPlayer *player)
{
	return false;
}

void ActorCubeChest::collideWithActor(ClientActor *actor)
{
	ChestLocoMotion *loc = static_cast<ChestLocoMotion *>(getLocoMotion());
	if (loc->m_Motion.Length() > GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.can_knock_up_motion && actor->getObjType() == OBJ_TYPE_MONSTER)
	{
		Rainbow::Vector3f dir = loc->m_Motion;
		dir.y = 0;
		actor->getLocoMotion()->addMotion(loc->m_Motion.x*GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ball_knock_up_motionX, GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ball_knock_up_motionY*dir.Length(), loc->m_Motion.z * GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.ball_knock_up_motionZ);

		ParticlesComponent::playParticles(actor, "ball_hit.ent");

		auto soundComp = actor->getSoundComponent();
		if (soundComp)
		{
			soundComp->playSound("ent.3420.hit", 1, 1, 6);
		}

		return;
	}

	//如果是和载具碰撞，检测碰撞到的方块是否是触碰方块，如果是触碰方块，需要进行触发相应的效果
	if (actor->getObjType() == OBJ_TYPE_VEHICLE)
	{
		ActorVehicleAssemble* vehicle = dynamic_cast<ActorVehicleAssemble*>(actor);
		int x = -1, y = -1, z = -1;
		vehicle->intersect(this, x, y, z);
		VehicleWorld* vehicleWorld = vehicle->getVehicleWorld();
		int blockid = vehicleWorld->getBlockID(x, y, z);
		if (blockid == BLOCK_BALLCOLLIDER)
		{
			auto material = g_BlockMtlMgr.getMaterial(blockid);
			if (material)
				material->onActorCollidedWithBlock(vehicleWorld, WCoord(x, y, z), this);
		}
	}

	applyActorCollision(this);
}

void ActorCubeChest::playBodyEffect(const char* fxname, bool sync/* =true */, float loopPlayTime /* = -1.0f */)
{
	if(m_Entity) m_Entity->PlayMotion(fxname, true, 0, loopPlayTime);

	//主机才去做广播
	if (!m_pWorld->isRemoteMode())
	{
		PB_PlayEffectHC playEffectHC;
		playEffectHC.set_effecttype(PB_EFFECT_STRINGACTORBODY);

		PB_EffectStringActorBody* actorBody = playEffectHC.mutable_stringactorbody();
		actorBody->set_objid(getObjId());
		actorBody->set_effectname(fxname);
		actorBody->set_status(0);
		actorBody->set_loopplaytime(loopPlayTime);

		m_pWorld->getMpActorMgr()->sendMsgToTrackingPlayers(PB_PLAYEFFECT_HC, playEffectHC, this, true);
	}
}

void ActorCubeChest::playBodyEffectClient(const char* fxname, float scale, float loopPlayTime /* = -1.0f */)
{
	if (m_Entity) m_Entity->PlayMotion(fxname, true, 0, loopPlayTime);

	//客机才发送到主机
	if (m_pWorld->isRemoteMode())
	{
		PB_PlayEffectCH playEffectCH;
		playEffectCH.set_effecttype(PB_EFFECT_STRINGACTORBODY);
		playEffectCH.set_effectscale(int(scale * 1000));

		PB_EffectStringActorBody* actorBody = playEffectCH.mutable_stringactorbody();
		if (actorBody == NULL) return;
		actorBody->set_objid(getObjId());
		if (fxname) actorBody->set_effectname(fxname);
		actorBody->set_status(0);
		actorBody->set_loopplaytime(loopPlayTime);

		GetGameNetManagerPtr()->sendToHost(PB_PLAYEFFECT_CH, playEffectCH);
	}
}

void ActorCubeChest::stopBodyEffect(const char* fxname, bool sync /* = true */)
{
	if(m_Entity) m_Entity->StopMotion(fxname);
}

void ActorCubeChest::kickedByPlayer(int type, float charge, ClientPlayer *player)
{
	return;
	if (!m_pWorld->isRemoteMode())
	{
		// player->doPutBall(this);
		auto bindAComponent = getBindActorCom();
		if (nullptr != bindAComponent)
		{
			bindAComponent->setBindInfo(-player->getObjId(), WCoord(0, 0, 0));
		}
		m_InvalidCatchTick = 5;
		m_KnockUpInvalidActor = player;
		ActorLocoMotion *playerloc = player->getLocoMotion();
		Rainbow::Vector3f dir = Yaw2FowardDir(playerloc->m_RotateYaw);

		float unit;
		float unitY;
		float motionY;
		float offsetMotionY;
		float minInitialV;
		if (type == PLAYEROP_SHOOT)
		{
			unit = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.shoot_uint;
			unitY = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.shoot_uintY;
			motionY = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.shoot_initial_motionY;
			offsetMotionY = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.shoot_offset_motionY / 88;
			minInitialV = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.shoot_initial_minV;
			if (!player->getLocoMotion()->m_OnGround)
			{
				minInitialV = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.shoot_jump_initial_minV;
				motionY = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.shoot_jump_initial_motionY;
			}
		}
		else if (type == PLAYEROP_PASS_BALL)
		{
			unit = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.pass_ball_uint;
			unitY = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.pass_ball_uintY;
			motionY = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.pb_initial_motionY;
			offsetMotionY = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.pb_offset_motionY / 88;
			minInitialV = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.pb_initial_minV;

			if (!player->getLocoMotion()->m_OnGround)
			{
				minInitialV = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.pb_jump_initial_minV;
				motionY = GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.pb_jump_initial_motionY;
			}
		}
		else
		{
			LOG_SEVERE("offsetMotionY NOT initialized");
			LOG_SEVERE("motionY NOT initialized");
			LOG_SEVERE("unitY NOT initialized");
			LOG_SEVERE("unit NOT initialized");
			LOG_SEVERE("minInitialV NOT initialized");
		}

		float pitch = playerloc->m_RotationPitch < 0 ? playerloc->m_RotationPitch : 0;
		motionY = -pitch*offsetMotionY + motionY + charge*unitY;

		ChestLocoMotion *ballloc = static_cast<ChestLocoMotion *>(getLocoMotion());
#ifdef USE_PHYSX
		ballloc->m_Motion = Rainbow::Vector3f(dir.x*(unit*charge + minInitialV), motionY, dir.z*(unit*charge + minInitialV));
		ballloc->doPickThrough(player);

		ballloc->m_PhysActor->SetAngularVelocity(Rainbow::Vector3f(
			(float)GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.kick_ball_angularX_v,
			(float)GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.kick_ball_angularY_v,
			(float)GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.kick_ball_angularZ_v));
		ballloc->m_PhysActor->SetLinearVelocity(ballloc->m_Motion * MOTION2VELOCITY);
#else
		ballloc->m_Motion.y = motionY;
		ballloc->addMotion(dir.x*unit*charge, 0, dir.z*unit*charge); //执行一次doPickThrough
#endif

		if (charge >= GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.strength_charge)
		{
			playBodyEffect("ball_power_high");
		}
		else
		{
			playBodyEffect("ball_power_low");
		}
	}



	if (charge >= GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.strength_charge)
	{
		auto soundComp = player->getSoundComponent();
		if (soundComp)
		{
			if (type == PLAYEROP_SHOOT)
			{
				soundComp->playSound("ent.3420.lob_power_high", 1.0f, 1.0f, 4);
			}
			else
			{
				soundComp->playSound("ent.3420.shoot_power_high", 1.0f, 1.0f, 4);
			}
		}
	}
	else
	{
		auto soundComp = player->getSoundComponent();
		if (soundComp)
		{
			if (type == PLAYEROP_SHOOT)
			{
				soundComp->playSound("ent.3420.lob_power_low", 1.0f, 1.0f, 4);
			}
			else
			{
				soundComp->playSound("ent.3420.shoot_power_low", 1.0f, 1.0f, 4);
			}
		}
	}
}

ActorCubeChest *ActorCubeChest::create(World *pworld, int x, int y, int z, float vx, float vy, float vz)
{
	ActorCubeChest *actor = SANDBOX_NEW(ActorCubeChest);

	if (vx > 0 && vy > 0 && vz>0) {
		actor->def_Width = vx;
		actor->def_Height = vy;
		actor->def_Deepth = vz;
	}
	actor->init();
	actor->getLocoMotion()->m_Motion = Rainbow::Vector3f(0, 0, 0);
	if (!pworld) return actor;
	ActorManager* actorMgr = dynamic_cast<ActorManager*>(pworld->getActorMgr());
	if (!actorMgr) return actor;
	actorMgr->spawnActor(actor, x, y, z, 0.0f, 0.0f);

	actor->m_SpawnPos = actor->getPosition();
	return actor;
}

void ActorCubeChest::bePushWithPlayer(ClientActor *player)
{
	
}

void ActorCubeChest::moveToPosition(const WCoord &pos, Rainbow::Quaternionf &rot, int interpol_ticks)
{
	ChestLocoMotion *loc = static_cast<ChestLocoMotion *>(getLocoMotion());

	loc->m_PosRotationIncrements = interpol_ticks;
	loc->m_ServerPos = pos;
	loc->m_ServerRot = rot;
}

void ActorCubeChest::setMotionChange(const Rainbow::Vector3f &motion, bool addmotion, bool changepos, bool sync_pos)
{
	ChestLocoMotion *ballloc = static_cast<ChestLocoMotion *>(getLocoMotion());

	ClientActor::setMotionChange(motion, addmotion, changepos, sync_pos);

	if(ballloc->m_PhysActor)
	{
		ballloc->m_PhysActor->SetLinearVelocity(ballloc->m_Motion * MOTION2VELOCITY);
		if(changepos)
		{
			//ballloc->m_PhysActor->SetPos(ballloc->m_Position.toVector3(), ballloc->m_RotateQuat);
		}
	}
}

void ActorCubeChest::resetRound()
{
	// 防止重置后的球类被重置的玩家带走到重置点
	if (m_InvalidCatchTick <= 0) m_InvalidCatchTick = 13;

	if (m_SpawnPos.x != 0 && m_SpawnPos.y >= 0 && m_SpawnPos.z != 0)
	{
		ClientActor *actor = nullptr;
		auto bindAComponent = getBindActorCom();
		if (bindAComponent)
		{
			actor = dynamic_cast<ClientActor *>(bindAComponent->getTarget());
		}
		if (actor)
		{
			bindAComponent->setBindInfo(-actor->getObjId(), WCoord(0, 0, 0));
		}
		getLocoMotion()->m_Motion.x = 0;
		getLocoMotion()->m_Motion.y = 0;
		getLocoMotion()->m_Motion.z = 0;

#ifdef USE_PHYSX
		static_cast<ChestLocoMotion *>(getLocoMotion())->detachPhysActor();
#endif
		getLocoMotion()->setPosition(m_SpawnPos.x, m_SpawnPos.y, m_SpawnPos.z);
#ifdef USE_PHYSX
		static_cast<ChestLocoMotion *>(getLocoMotion())->attachPhysActor();
#endif
	}
}

bool ActorCubeChest::interact(ClientActor *pPlayer, bool onshift /* = false */, bool isMobile)
{
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(pPlayer);
	if (nullptr != pTempPlayer)
	{
		return leftClickInteract(pTempPlayer);
	}
	else
	{
		return false;
	}
}

bool ActorCubeChest::leftClickInteract(ClientActor *player)
{
	//Rainbow::Vector3f dir = Yaw2FowardDir(player->getLocoMotion()->m_RotateYaw);
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		ClientActor* actor = nullptr;
	}
	return true;
}


void ActorCubeChest::playSoundByPhysCollision()
{
	auto physActor = static_cast<ChestLocoMotion *>(getLocoMotion())->m_PhysActor;
	if (physActor && physActor->GetLinearVelocity().Length() > GetWorldManagerPtr()->m_SurviveGameConfig->ballconfig.threshold)
	{
		//char soundname[32];
		//sprintf(soundname, "ent.3420.kick%d", GenRandomInt(1, 6));
		//m_pWorld->getEffectMgr()->playSound(getPosition(), "ent.3420.kick1", 1.0f, 1.0f, PLAYSND_SYNC | PLAYSND_LONGDIST);
	}
}

void ActorCubeChest::dribblingRotate(Rainbow::Vector3f motion, float yaw)
{
	motion.y = 0;
	if (motion.Length() > 0)
	{
		m_Dribbing = true;
		m_DribbingMotion = motion;
		m_KeepdribbingTick = 2;
	}
	else
		m_Dribbing = false;
}

bool ActorCubeChest::isPhysics()
{
	ChestLocoMotion* pLoco = dynamic_cast<ChestLocoMotion*>(getLocoMotion());
	if (pLoco) {
		return pLoco->m_hasPhysActor;
	}

	return false;
}

void ActorCubeChest::onClear()
{
	if (this->isPhysics())
	{
		ParticlesComponent::playParticles(this, "10021.ent");
	}
}

BindActorComponent*  ActorCubeChest::getBindActorCom()
{
	 
	return nullptr;
	 
}


long long ActorCubeChest::getBindTargetID()
{
	return 0; 
}


bool ActorCubeChest::isStoppedMoving()
{
	return false;
}